<template>
  <!-- Flowbite Pro Modal -->
  <div
    v-if="isOpen"
    id="grade-boundary-modal"
    tabindex="-1"
    aria-hidden="true"
    class="fixed inset-0 z-50 flex items-center justify-center p-4 bg-gray-900 bg-opacity-50"
    @click.self="closeModal"
  >
    <div class="relative w-full max-w-2xl max-h-full">
      <!-- Modal content -->
      <div class="relative bg-white rounded-lg shadow-xl border border-gray-200">
        <div class="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
          <div class="sm:flex sm:items-start">
            <div class="mt-3 text-center sm:mt-0 sm:text-left w-full">
              <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">
                {{ isEditing ? 'Edit Grade Boundary' : 'Add New Grade Boundary' }}
              </h3>

              <form @submit.prevent="handleSubmit" class="space-y-4">
                <!-- Subject -->
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-2">Subject</label>
                  <select
                    v-model="formData.subjectId"
                    required
                    :disabled="!!preSelectedSubject"
                    @change="onSubjectChange"
                    class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-maneb-primary focus:border-maneb-primary text-sm disabled:bg-gray-100"
                  >
                    <option value="">Select a subject</option>
                    <option v-for="subject in gradingStore.activeSubjects" :key="subject.id" :value="subject.id">
                      {{ subject.name }} ({{ subject.code }})
                    </option>
                  </select>
                </div>

                <!-- Paper -->
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-2">Paper</label>
                  <select
                    v-model="formData.paperId"
                    required
                    :disabled="!formData.subjectId"
                    class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-maneb-primary focus:border-maneb-primary text-sm disabled:bg-gray-100"
                  >
                    <option value="">Select a paper</option>
                    <option v-for="paper in availablePapers" :key="paper.id" :value="paper.id">
                      {{ paper.name }}
                    </option>
                  </select>
                </div>

                <!-- Year -->
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-2">Academic Year</label>
                  <input
                    v-model.number="formData.year"
                    type="number"
                    required
                    min="2020"
                    :max="new Date().getFullYear() + 5"
                    :disabled="!!preSelectedYear"
                    placeholder="Enter academic year"
                    class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-maneb-primary focus:border-maneb-primary text-sm disabled:bg-gray-100"
                  />
                </div>

                <!-- Grade Level -->
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-2">Grade Level</label>
                  <select
                    v-model="formData.gradeLevel"
                    required
                    class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-maneb-primary focus:border-maneb-primary text-sm"
                  >
                    <option value="">Select a grade level</option>
                    <option value="A">A (Distinction)</option>
                    <option value="B">B (Credit)</option>
                    <option value="C">C (Pass)</option>
                    <option value="D">D (Pass)</option>
                    <option value="F">F (Fail)</option>
                  </select>
                </div>

                <!-- Score Range -->
                <div class="grid grid-cols-2 gap-4">
                  <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Minimum Score</label>
                    <input
                      v-model.number="formData.minScore"
                      type="number"
                      required
                      min="0"
                      max="100"
                      placeholder="Min score"
                      class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-maneb-primary focus:border-maneb-primary text-sm"
                    />
                  </div>
                  <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Maximum Score</label>
                    <input
                      v-model.number="formData.maxScore"
                      type="number"
                      required
                      min="0"
                      max="100"
                      placeholder="Max score"
                      class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-maneb-primary focus:border-maneb-primary text-sm"
                    />
                  </div>
                </div>

                <!-- Description -->
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-2">Description</label>
                  <input
                    v-model="formData.description"
                    type="text"
                    placeholder="Enter description (optional)"
                    class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-maneb-primary focus:border-maneb-primary text-sm"
                  />
                </div>

                <!-- Active Status -->
                <div class="flex items-center">
                  <input
                    v-model="formData.isActive"
                    type="checkbox"
                    id="isActive"
                    class="h-4 w-4 text-maneb-primary focus:ring-maneb-primary border-gray-300 rounded"
                  />
                  <label for="isActive" class="ml-2 block text-sm text-gray-700">
                    Grade boundary is active
                  </label>
                </div>

                <!-- Error Message -->
                <div v-if="error" class="bg-red-50 border border-red-200 rounded-lg p-3">
                  <p class="text-sm text-red-600">{{ error }}</p>
                </div>

                <!-- Validation Warnings -->
                <div v-if="validationWarnings.length > 0" class="bg-yellow-50 border border-yellow-200 rounded-lg p-3">
                  <p class="text-sm font-medium text-yellow-800 mb-1">Validation Warnings:</p>
                  <ul class="text-sm text-yellow-700 list-disc list-inside">
                    <li v-for="warning in validationWarnings" :key="warning">{{ warning }}</li>
                  </ul>
                </div>

                <!-- Grade Boundary Preview -->
                <div v-if="formData.gradeLevel && formData.minScore !== null && formData.maxScore !== null" class="bg-gray-50 border border-gray-200 rounded-lg p-4">
                  <h4 class="text-sm font-medium text-gray-900 mb-2">Grade Boundary Preview</h4>
                  <div class="flex items-center justify-between">
                    <div>
                      <span class="text-lg font-semibold" :class="getGradeBadgeClass(formData.gradeLevel)">
                        Grade {{ formData.gradeLevel }}
                      </span>
                      <div class="text-sm text-gray-600">{{ formData.description || getDefaultDescription(formData.gradeLevel) }}</div>
                    </div>
                    <div class="text-right">
                      <div class="text-lg font-medium text-gray-900">{{ formData.minScore }}% - {{ formData.maxScore }}%</div>
                      <div class="text-sm text-gray-600">Score Range</div>
                    </div>
                  </div>
                </div>
              </form>
            </div>
          </div>
        </div>

        <!-- Modal Actions -->
        <div class="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
          <button
            @click="handleSubmit"
            :disabled="isLoading || !isFormValid"
            class="w-full inline-flex justify-center rounded-lg border border-transparent shadow-sm px-4 py-2 bg-maneb-primary text-base font-medium text-white hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-maneb-primary sm:ml-3 sm:w-auto sm:text-sm disabled:opacity-50 disabled:cursor-not-allowed"
          >
            <svg v-if="isLoading" class="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
              <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
              <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
            {{ isLoading ? 'Saving...' : (isEditing ? 'Update Boundary' : 'Add Boundary') }}
          </button>
          <button
            @click="closeModal"
            type="button"
            class="mt-3 w-full inline-flex justify-center rounded-lg border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-maneb-primary sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm"
          >
            Cancel
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { useGradingStore } from '@/store'
import type { GradeBoundaryDto, GradeLevel, CreateGradeBoundaryRequest, UpdateGradeBoundaryRequest } from '@/interfaces'
import sweetAlert from '@/utils/sweetAlert'

// Props
interface Props {
  isOpen: boolean
  boundary?: GradeBoundaryDto | null
  preSelectedSubject?: string
  preSelectedYear?: number | null
}

const props = withDefaults(defineProps<Props>(), {
  isOpen: false,
  boundary: null,
  preSelectedSubject: '',
  preSelectedYear: null
})

// Emits
const emit = defineEmits<{
  close: []
  success: []
}>()

// Store
const gradingStore = useGradingStore()

// State
const formData = ref<{
  subjectId: string
  paperId: string
  year: number
  gradeLevel: GradeLevel | ''
  minScore: number
  maxScore: number
  description: string
  isActive: boolean
}>({
  subjectId: '',
  paperId: '',
  year: new Date().getFullYear(),
  gradeLevel: '',
  minScore: 0,
  maxScore: 0,
  description: '',
  isActive: true
})

const isLoading = ref(false)
const error = ref<string | null>(null)

// Computed
const isEditing = computed(() => !!props.boundary?.id)

const isFormValid = computed(() => {
  return formData.value.subjectId &&
         formData.value.paperId &&
         formData.value.year &&
         formData.value.gradeLevel &&
         formData.value.minScore >= 0 &&
         formData.value.maxScore >= 0 &&
         formData.value.minScore <= formData.value.maxScore &&
         formData.value.maxScore <= 100
})

const availablePapers = computed(() => {
  if (!formData.value.subjectId) return []
  return gradingStore.papers.filter(paper =>
    paper.subjectId === formData.value.subjectId && paper.isActive && !paper.isDeleted
  )
})

const validationWarnings = computed(() => {
  const warnings: string[] = []
  
  if (formData.value.minScore > formData.value.maxScore) {
    warnings.push('Minimum score cannot be greater than maximum score')
  }
  
  if (formData.value.gradeLevel === 'F' && formData.value.maxScore >= 50) {
    warnings.push('Grade F typically has a maximum score below 50%')
  }
  
  if (formData.value.gradeLevel === 'A' && formData.value.minScore < 80) {
    warnings.push('Grade A typically has a minimum score of 80% or higher')
  }
  
  return warnings
})

// Methods
const getGradeBadgeClass = (grade: string): string => {
  const classes = {
    'A': 'text-green-700',
    'B': 'text-blue-700',
    'C': 'text-yellow-700',
    'D': 'text-orange-700',
    'F': 'text-red-700'
  }
  return classes[grade as keyof typeof classes] || 'text-gray-700'
}

const getDefaultDescription = (grade: string): string => {
  const descriptions = {
    'A': 'Distinction',
    'B': 'Credit',
    'C': 'Pass',
    'D': 'Pass',
    'F': 'Fail'
  }
  return descriptions[grade as keyof typeof descriptions] || ''
}

const resetForm = () => {
  formData.value = {
    subjectId: props.preSelectedSubject || '',
    year: props.preSelectedYear || new Date().getFullYear(),
    gradeLevel: '',
    minScore: 0,
    maxScore: 0,
    description: '',
    isActive: true
  }
  error.value = null
}

const closeModal = () => {
  resetForm()
  emit('close')
}

const handleSubmit = async () => {
  if (!isFormValid.value) return

  isLoading.value = true
  error.value = null

  try {
    if (isEditing.value && props.boundary?.id) {
      // Update existing boundary
      const updateData: UpdateGradeBoundaryRequest = {
        subjectId: formData.value.subjectId,
        year: formData.value.year,
        gradeLevel: formData.value.gradeLevel as GradeLevel,
        minScore: formData.value.minScore,
        maxScore: formData.value.maxScore,
        description: formData.value.description || undefined,
        isActive: formData.value.isActive
      }
      await gradingStore.updateGradeBoundary(props.boundary.id, updateData)
      await sweetAlert.toast.success('Grade boundary updated successfully')
    } else {
      // Create new boundary
      const createData: CreateGradeBoundaryRequest = {
        subjectId: formData.value.subjectId,
        year: formData.value.year,
        gradeLevel: formData.value.gradeLevel as GradeLevel,
        minScore: formData.value.minScore,
        maxScore: formData.value.maxScore,
        description: formData.value.description || undefined,
        isActive: formData.value.isActive
      }
      await gradingStore.createGradeBoundary(createData)
      await sweetAlert.toast.success('Grade boundary added successfully')
    }

    emit('success')
  } catch (err: any) {
    error.value = err.message || 'Failed to save grade boundary'
  } finally {
    isLoading.value = false
  }
}

// Watch for boundary prop changes
watch(() => props.boundary, (newBoundary) => {
  if (newBoundary) {
    formData.value = {
      subjectId: newBoundary.subjectId || '',
      year: newBoundary.year || new Date().getFullYear(),
      gradeLevel: newBoundary.gradeLevel || '',
      minScore: newBoundary.minScore || 0,
      maxScore: newBoundary.maxScore || 0,
      description: newBoundary.description || '',
      isActive: newBoundary.isActive ?? true
    }
  } else {
    resetForm()
  }
}, { immediate: true })

// Watch for modal open/close
watch(() => props.isOpen, (isOpen) => {
  if (!isOpen) {
    resetForm()
  } else {
    // Set pre-selected values when modal opens
    if (props.preSelectedSubject) {
      formData.value.subjectId = props.preSelectedSubject
    }
    if (props.preSelectedYear) {
      formData.value.year = props.preSelectedYear
    }
  }
})
</script>

<style scoped>
/* MANEB Theme Colors */
.bg-maneb-primary {
  background-color: #a12c2c;
}

.text-maneb-primary {
  color: #a12c2c;
}

.border-maneb-primary {
  border-color: #a12c2c;
}

.hover\:bg-maneb-primary:hover {
  background-color: #a12c2c;
}

.focus\:ring-maneb-primary:focus {
  --tw-ring-color: #a12c2c;
}

.focus\:border-maneb-primary:focus {
  border-color: #a12c2c;
}
</style>
